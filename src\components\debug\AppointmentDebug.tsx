import React from 'react';
import { useAppointments, useActiveAppointment } from '../../hooks/useAppointments';
import { AppointmentService } from '../../services/appointment.service';
import Button from '../ui/button/Button';
import toast from 'react-hot-toast';

const AppointmentDebug: React.FC = () => {
  const { data: allAppointments, isLoading: allLoading, error: allError, refetch: refetchAll } = useAppointments();
  const { data: activeAppointment, isLoading: activeLoading, error: activeError, refetch: refetchActive } = useActiveAppointment();

  // Client-side filter for InProgress appointments
  const inProgressAppointments = React.useMemo(() => {
    return allAppointments?.filter(apt => apt.status === 'InProgress') || [];
  }, [allAppointments]);

  const handleSetInProgress = async (appointmentId: number) => {
    try {
      await AppointmentService.updateAppointmentStatus(appointmentId, {
        status: 'InProgress',
        notes: 'Set to InProgress for testing',
        realAppointmentStartTime: new Date().toISOString()
      });
      toast.success('Appointment set to InProgress');
      // Refetch all data
      refetchAll();
      refetchActive();
    } catch (error) {
      console.error('Error setting appointment to InProgress:', error);
      toast.error('Failed to set appointment to InProgress');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Appointment Debug Info
      </h3>
      
      <div className="space-y-4 text-sm">
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white">All Appointments:</h4>
          <p className="text-gray-600 dark:text-gray-400">
            Loading: {allLoading ? 'Yes' : 'No'} | 
            Error: {allError ? 'Yes' : 'No'} | 
            Count: {allAppointments?.length || 0}
          </p>
          {allAppointments && (
            <div className="mt-2">
              <p className="text-xs text-gray-500">Statuses:</p>
              <ul className="text-xs text-gray-600 dark:text-gray-400">
                {allAppointments.map(apt => (
                  <li key={apt.id} className="flex items-center justify-between py-1">
                    <span>
                      ID: {apt.id} | Status: {apt.status} | Service: {apt.service?.title || 'N/A'}
                    </span>
                    {apt.status !== 'InProgress' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleSetInProgress(apt.id)}
                        className="ml-2 text-xs py-1 px-2"
                      >
                        Set InProgress
                      </Button>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div>
          <h4 className="font-medium text-gray-900 dark:text-white">InProgress Appointments (client-side filtered):</h4>
          <p className="text-gray-600 dark:text-gray-400">
            Count: {inProgressAppointments.length}
          </p>
          {inProgressAppointments.length > 0 && (
            <div className="mt-2">
              <ul className="text-xs text-gray-600 dark:text-gray-400">
                {inProgressAppointments.map(apt => (
                  <li key={apt.id}>
                    ID: {apt.id} | Status: {apt.status} | Service: {apt.service?.title || 'N/A'}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div>
          <h4 className="font-medium text-gray-900 dark:text-white">Active Appointment (useActiveAppointment):</h4>
          <p className="text-gray-600 dark:text-gray-400">
            Loading: {activeLoading ? 'Yes' : 'No'} | 
            Error: {activeError ? 'Yes' : 'No'} | 
            Found: {activeAppointment ? 'Yes' : 'No'}
          </p>
          {activeAppointment && (
            <div className="mt-2">
              <p className="text-xs text-gray-600 dark:text-gray-400">
                ID: {activeAppointment.id} | Status: {activeAppointment.status} | 
                Service: {activeAppointment.service?.title || 'N/A'}
              </p>
            </div>
          )}
        </div>

        {(allError || activeError) && (
          <div>
            <h4 className="font-medium text-red-600">Errors:</h4>
            {allError && <p className="text-xs text-red-500">All: {String(allError)}</p>}
            {activeError && <p className="text-xs text-red-500">Active: {String(activeError)}</p>}
          </div>
        )}
      </div>
    </div>
  );
};

export default AppointmentDebug;
