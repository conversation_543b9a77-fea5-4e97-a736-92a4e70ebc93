import React from 'react';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';
import { formatLocalTime } from '../../utils/timezone';

// Mock data - will be replaced with real API data
const mockRecentAppointments = [
  {
    id: 1,
    customer: { firstName: '<PERSON>', lastName: '<PERSON>' },
    service: { title: 'Hair Cut & Style', price: 75 },
    expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
    status: 'completed'
  },
  {
    id: 2,
    customer: { firstName: '<PERSON>', lastName: '<PERSON>' },
    service: { title: 'Color Treatment', price: 150 },
    expectedAppointmentStartTime: '2024-01-15T14:00:00Z',
    status: 'completed'
  },
  {
    id: 3,
    customer: { firstName: 'Emily', lastName: 'Chen' },
    service: { title: 'Consultation', price: 25 },
    expectedAppointmentStartTime: '2024-01-16T09:30:00Z',
    status: 'completed'
  },
  {
    id: 4,
    customer: { firstName: 'John', lastName: 'Smith' },
    service: { title: 'Deep Conditioning', price: 60 },
    expectedAppointmentStartTime: '2024-01-16T11:00:00Z',
    status: 'completed'
  },
  {
    id: 5,
    customer: { firstName: 'Lisa', lastName: 'Wilson' },
    service: { title: 'Styling Session', price: 100 },
    expectedAppointmentStartTime: '2024-01-16T15:30:00Z',
    status: 'completed'
  }
];

const RecentAppointmentsSimple: React.FC = () => {
  const navigate = useNavigate();

  const totalRevenue = mockRecentAppointments
    .filter(apt => apt.status === 'completed')
    .reduce((sum, apt) => sum + apt.service.price, 0);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Appointments
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            ${totalRevenue.toLocaleString()} from {mockRecentAppointments.length} completed appointments
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/appointments')}
        >
          View All
        </Button>
      </div>

      <div className="space-y-3">
        {mockRecentAppointments.map((appointment) => (
          <div
            key={appointment.id}
            className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
            onClick={() => navigate('/appointments')}
          >
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                {appointment.customer.firstName.charAt(0)}{appointment.customer.lastName.charAt(0)}
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {appointment.customer.firstName} {appointment.customer.lastName}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {appointment.service.title}
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="font-medium text-gray-900 dark:text-white">
                ${appointment.service.price}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {formatLocalTime(appointment.expectedAppointmentStartTime)}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {mockRecentAppointments.length}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Appointments</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              ${totalRevenue.toLocaleString()}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Total Revenue</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              ${Math.round(totalRevenue / mockRecentAppointments.length)}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Avg per appointment</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentAppointmentsSimple;
