import React from 'react';
import { QueueStats } from '../../types/queue';

interface QueueStatsOverviewProps {
  stats: QueueStats;
}

export default function QueueStatsOverview({ stats }: QueueStatsOverviewProps) {
  const formatTime = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 80) return 'text-green-600 dark:text-green-400';
    if (efficiency >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
        Queue Overview
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {/* Total Queues */}
        <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {stats.totalQueues}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Total Queues
          </div>
        </div>

        {/* Active Queues */}
        <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {stats.activeQueues}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Active Queues
          </div>
        </div>

        {/* Total Customers Today */}
        <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {stats.totalCustomersToday}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Customers Today
          </div>
        </div>

        {/* Average Wait Time */}
        <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {formatTime(stats.averageWaitTime)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Avg Wait Time
          </div>
        </div>

        {/* Queue Efficiency */}
        <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className={`text-2xl font-bold ${getEfficiencyColor(stats.queueEfficiency)}`}>
            {Math.round(stats.queueEfficiency)}%
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Efficiency
          </div>
        </div>

        {/* Busy Queues */}
        <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {stats.busyQueues?.length || 0}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Busy Queues
          </div>
        </div>
      </div>

      {/* Busy Queues Details */}
      {stats.busyQueues && stats.busyQueues.length > 0 && (
        <div className="mt-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
            Queues Needing Attention
          </h4>
          <div className="space-y-2">
            {stats.busyQueues.slice(0, 3).map((queue) => (
              <div 
                key={queue.id}
                className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {queue.title}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {queue.currentPosition || 0} customers waiting
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-red-600 dark:text-red-400">
                    {formatTime(queue.estimatedWaitTime || 0)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    wait time
                  </div>
                </div>
              </div>
            ))}
            {stats.busyQueues.length > 3 && (
              <div className="text-center py-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  +{stats.busyQueues.length - 3} more queues need attention
                </span>
              </div>
            )}
          </div>
        </div>
      )}


    </div>
  );
}
