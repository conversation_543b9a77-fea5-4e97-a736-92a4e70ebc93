import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { AppointmentService } from '../appointment.service';
import { apiClient } from '../../lib/api-client';
import { AppointmentCreateRequest, AppointmentStatus } from '../../types';

// Mock the API client
vi.mock('../../lib/api-client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock config
vi.mock('../../lib/config', () => ({
  config: {
    endpoints: {
      appointments: {
        base: '/api/auth/providers/appointments',
      },
      customers: {
        base: '/api/auth/customers',
      },
    },
  },
}));

describe('AppointmentService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getAppointments', () => {
    it('should fetch appointments successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            appointments: [
              {
                id: 1,
                status: 'confirmed',
                expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
                expectedAppointmentEndTime: '2024-01-15T10:30:00Z',
              },
            ],
            pagination: {
              page: 1,
              limit: 10,
              total: 1,
              totalPages: 1,
              hasNext: false,
              hasPrev: false,
            },
          },
          message: 'Success',
        },
      };

      (apiClient.get as Mock).mockResolvedValue(mockResponse);

      const result = await AppointmentService.getAppointments();

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/auth/providers/appointments',
        { params: undefined }
      );
      expect(result).toEqual(mockResponse.data.data.appointments);
    });

    it('should handle filters correctly', async () => {
      const filters = { status: 'confirmed' as AppointmentStatus };
      const mockResponse = {
        data: {
          success: true,
          data: { appointments: [], pagination: {} },
          message: 'Success',
        },
      };

      (apiClient.get as Mock).mockResolvedValue(mockResponse);

      await AppointmentService.getAppointments(filters);

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/auth/providers/appointments',
        { params: filters }
      );
    });
  });

  describe('createAppointment', () => {
    it('should create appointment successfully', async () => {
      const appointmentData: AppointmentCreateRequest = {
        customerId: 'customer-123',
        serviceId: 1,
        placeId: 1,
        queueId: 1,
        expectedStartTime: '2024-01-15T10:00:00Z',
        expectedEndTime: '2024-01-15T10:30:00Z',
        serviceDuration: 30,
        notes: 'Test appointment',
        slots: 1,
      };

      const mockResponse = {
        data: {
          success: true,
          data: {
            id: 1,
            ...appointmentData,
            status: 'pending',
          },
          message: 'Appointment created successfully',
        },
      };

      (apiClient.post as Mock).mockResolvedValue(mockResponse);

      const result = await AppointmentService.createAppointment(appointmentData);

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/providers/appointments',
        appointmentData
      );
      expect(result).toEqual(mockResponse.data.data);
    });
  });

  describe('validateAppointmentCreation', () => {
    it('should validate appointment creation successfully', async () => {
      const appointmentData: AppointmentCreateRequest = {
        customerId: 'customer-123',
        serviceId: 1,
        placeId: 1,
        queueId: 1,
        expectedStartTime: '2024-01-15T10:00:00Z',
        expectedEndTime: '2024-01-15T10:30:00Z',
        serviceDuration: 30,
        slots: 1,
      };

      // Mock credits check
      (apiClient.get as Mock).mockResolvedValueOnce({
        data: {
          success: true,
          data: { credits: 5 },
          message: 'Success',
        },
      });

      // Mock conflict check
      (apiClient.post as Mock).mockResolvedValueOnce({
        data: {
          success: true,
          data: { hasConflict: false, conflictingAppointments: [] },
          message: 'No conflicts',
        },
      });

      const result = await AppointmentService.validateAppointmentCreation(appointmentData);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect insufficient credits', async () => {
      const appointmentData: AppointmentCreateRequest = {
        customerUserId: 'customer-123',
        serviceId: 1,
        placeId: 1,
        queueId: 1,
        expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
        expectedAppointmentEndTime: '2024-01-15T10:30:00Z',
        serviceDuration: 30,
        slots: 1,
      };

      // Mock insufficient credits
      (apiClient.get as Mock).mockResolvedValueOnce({
        data: {
          success: true,
          data: { credits: 0 },
          message: 'Success',
        },
      });

      const result = await AppointmentService.validateAppointmentCreation(appointmentData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Insufficient credits. At least 1 credit is required to create an appointment.');
    });

    it('should detect time slot conflicts', async () => {
      const appointmentData: AppointmentCreateRequest = {
        customerUserId: 'customer-123',
        serviceId: 1,
        placeId: 1,
        queueId: 1,
        expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
        expectedAppointmentEndTime: '2024-01-15T10:30:00Z',
        serviceDuration: 30,
        slots: 1,
      };

      // Mock sufficient credits
      (apiClient.get as Mock).mockResolvedValueOnce({
        data: {
          success: true,
          data: { credits: 5 },
          message: 'Success',
        },
      });

      // Mock conflict detected
      (apiClient.post as Mock).mockResolvedValueOnce({
        data: {
          success: true,
          data: { hasConflict: true, conflictingAppointments: [{ id: 1 }] },
          message: 'Conflict detected',
        },
      });

      const result = await AppointmentService.validateAppointmentCreation(appointmentData);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Time slot conflict detected. Please choose a different time.');
    });
  });

  describe('validateStatusTransition', () => {
    it('should allow valid status transitions', () => {
      const validTransitions = [
        { from: 'pending', to: 'confirmed' },
        { from: 'pending', to: 'canceled' },
        { from: 'confirmed', to: 'InProgress' },
        { from: 'confirmed', to: 'canceled' },
        { from: 'confirmed', to: 'noshow' },
        { from: 'InProgress', to: 'completed' },
        { from: 'InProgress', to: 'canceled' },
      ];

      validTransitions.forEach(({ from, to }) => {
        const result = AppointmentService.validateStatusTransition(
          from as AppointmentStatus,
          to as AppointmentStatus
        );
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });
    });

    it('should reject invalid status transitions', () => {
      const invalidTransitions = [
        { from: 'completed', to: 'pending' },
        { from: 'canceled', to: 'confirmed' },
        { from: 'noshow', to: 'InProgress' },
        { from: 'pending', to: 'InProgress' }, // Must go through confirmed first
      ];

      invalidTransitions.forEach(({ from, to }) => {
        const result = AppointmentService.validateStatusTransition(
          from as AppointmentStatus,
          to as AppointmentStatus
        );
        expect(result.isValid).toBe(false);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('updateAppointmentStatus', () => {
    it('should update appointment status successfully', async () => {
      const statusUpdate = {
        status: 'confirmed' as AppointmentStatus,
        changeReason: 'Provider confirmed',
      };

      const mockResponse = {
        data: {
          success: true,
          data: {
            id: 1,
            status: 'confirmed',
            expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
            expectedAppointmentEndTime: '2024-01-15T10:30:00Z',
          },
          message: 'Status updated successfully',
        },
      };

      (apiClient.patch as Mock).mockResolvedValue(mockResponse);

      const result = await AppointmentService.updateAppointmentStatus(1, statusUpdate);

      expect(apiClient.patch).toHaveBeenCalledWith(
        '/api/auth/providers/appointments/1/status',
        statusUpdate
      );
      expect(result).toEqual(mockResponse.data.data);
    });
  });

  describe('deleteAppointment', () => {
    it('should delete appointment successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Appointment deleted successfully',
        },
      };

      (apiClient.delete as Mock).mockResolvedValue(mockResponse);

      await AppointmentService.deleteAppointment(1);

      expect(apiClient.delete).toHaveBeenCalledWith('/api/auth/providers/appointments/1');
    });

    it('should throw error if deletion fails', async () => {
      const mockResponse = {
        data: {
          success: false,
          message: 'Failed to delete appointment',
        },
      };

      (apiClient.delete as Mock).mockResolvedValue(mockResponse);

      await expect(AppointmentService.deleteAppointment(1)).rejects.toThrow('Failed to delete appointment');
    });
  });
});
