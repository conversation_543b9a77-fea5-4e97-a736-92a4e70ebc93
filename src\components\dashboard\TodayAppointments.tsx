import React from 'react';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';
import { formatLocalTime } from '../../utils/timezone';

// Mock data - will be replaced with real API data
const mockTodayAppointments = [
  {
    id: 1,
    customer: { firstName: '<PERSON>', lastName: '<PERSON>' },
    service: { title: 'Hair Cut & Style', duration: 60 },
    expectedAppointmentStartTime: '2024-01-15T10:00:00Z',
    status: 'confirmed'
  },
  {
    id: 2,
    customer: { firstName: '<PERSON>', lastName: '<PERSON>' },
    service: { title: 'Color Treatment', duration: 120 },
    expectedAppointmentStartTime: '2024-01-15T14:00:00Z',
    status: 'pending'
  },
  {
    id: 3,
    customer: { firstName: 'Emily', lastName: 'Chen' },
    service: { title: 'Consultation', duration: 30 },
    expectedAppointmentStartTime: '2024-01-15T16:30:00Z',
    status: 'confirmed'
  }
];

const TodayAppointments: React.FC = () => {
  const navigate = useNavigate();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'InProgress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getNextAppointment = () => {
    const now = new Date();
    return mockTodayAppointments
      .filter(apt => new Date(apt.expectedAppointmentStartTime) > now)
      .sort((a, b) => new Date(a.expectedAppointmentStartTime).getTime() - new Date(b.expectedAppointmentStartTime).getTime())[0];
  };

  const nextAppointment = getNextAppointment();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Today's Appointments
        </h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/appointments')}
        >
          View All
        </Button>
      </div>

      {mockTodayAppointments.length === 0 ? (
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-gray-600 dark:text-gray-400">No appointments today</p>
        </div>
      ) : (
        <div className="space-y-3">
          {/* Next Appointment Highlight */}
          {nextAppointment && (
            <div className="bg-brand-50 dark:bg-brand-900/20 border border-brand-200 dark:border-brand-800 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-2 h-2 bg-brand-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-brand-700 dark:text-brand-300">Next Appointment</span>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {nextAppointment.customer.firstName} {nextAppointment.customer.lastName}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {nextAppointment.service.title}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-brand-700 dark:text-brand-300">
                    {formatLocalTime(nextAppointment.expectedAppointmentStartTime)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {nextAppointment.service.duration} min
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* All Appointments List */}
          <div className="space-y-2">
            {mockTodayAppointments.slice(0, 4).map((appointment) => (
              <div
                key={appointment.id}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                onClick={() => navigate('/appointments')}
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                    {appointment.customer.firstName.charAt(0)}{appointment.customer.lastName.charAt(0)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white text-sm">
                      {appointment.customer.firstName} {appointment.customer.lastName}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {appointment.service.title}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatLocalTime(appointment.expectedAppointmentStartTime)}
                  </p>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                    {appointment.status}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {mockTodayAppointments.length > 4 && (
            <div className="text-center pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/appointments')}
                className="text-brand-600 hover:text-brand-700"
              >
                +{mockTodayAppointments.length - 4} more appointments
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TodayAppointments;
