import { useState, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router';

export interface SearchResult {
  id: string;
  title: string;
  description: string;
  path: string;
  category: 'page' | 'feature' | 'action';
  icon?: string;
  keywords?: string[];
}

// Define all searchable pages and features
const searchableItems: SearchResult[] = [
  // Main Pages
  {
    id: 'dashboard',
    title: 'Dashboard',
    description: 'Overview of your business metrics and activities',
    path: '/',
    category: 'page',
    icon: '📊',
    keywords: ['home', 'overview', 'metrics', 'analytics', 'stats']
  },
  {
    id: 'calendar',
    title: 'Calendar',
    description: 'View and manage appointments in calendar format',
    path: '/calendar',
    category: 'page',
    icon: '📅',
    keywords: ['schedule', 'appointments', 'booking', 'time', 'date']
  },
  {
    id: 'appointments',
    title: 'Appointments',
    description: 'Manage all your appointments and bookings',
    path: '/appointments',
    category: 'page',
    icon: '📋',
    keywords: ['bookings', 'schedule', 'clients', 'meetings']
  },
  {
    id: 'customers',
    title: 'Customers',
    description: 'Manage your customer database and relationships',
    path: '/customers',
    category: 'page',
    icon: '👥',
    keywords: ['clients', 'contacts', 'crm', 'people', 'database']
  },
  {
    id: 'services',
    title: 'Services',
    description: 'Configure your services, pricing, and offerings',
    path: '/services',
    category: 'page',
    icon: '⚙️',
    keywords: ['offerings', 'pricing', 'setup', 'configuration', 'products']
  },
  {
    id: 'locations',
    title: 'Locations',
    description: 'Manage your business locations and addresses',
    path: '/locations',
    category: 'page',
    icon: '📍',
    keywords: ['addresses', 'places', 'venues', 'setup', 'branches']
  },
  {
    id: 'queues',
    title: 'Queues',
    description: 'Manage waiting queues and walk-in customers',
    path: '/queues',
    category: 'page',
    icon: '🚶',
    keywords: ['waiting', 'walk-in', 'line', 'queue management']
  },
  {
    id: 'profile',
    title: 'Profile',
    description: 'Manage your provider profile and settings',
    path: '/profile',
    category: 'page',
    icon: '👤',
    keywords: ['account', 'settings', 'personal', 'information', 'details']
  },
  {
    id: 'advanced',
    title: 'Advanced Features',
    description: 'Access advanced tools and features',
    path: '/advanced',
    category: 'page',
    icon: '🔧',
    keywords: ['tools', 'advanced', 'features', 'settings']
  },

  // Quick Actions
  {
    id: 'new-appointment',
    title: 'New Appointment',
    description: 'Create a new appointment booking',
    path: '/calendar',
    category: 'action',
    icon: '➕',
    keywords: ['create', 'book', 'schedule', 'add', 'new booking']
  },
  {
    id: 'add-customer',
    title: 'Add Customer',
    description: 'Add a new customer to your database',
    path: '/customers',
    category: 'action',
    icon: '👤',
    keywords: ['create', 'new client', 'add contact', 'register']
  },
  {
    id: 'add-service',
    title: 'Add Service',
    description: 'Create a new service offering',
    path: '/services',
    category: 'action',
    icon: '⚙️',
    keywords: ['create', 'new service', 'offering', 'setup']
  },
  {
    id: 'add-location',
    title: 'Add Location',
    description: 'Add a new business location',
    path: '/locations',
    category: 'action',
    icon: '📍',
    keywords: ['create', 'new location', 'address', 'venue']
  }
];

export const useGlobalSearch = () => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  // Filter and rank search results
  const searchResults = useMemo(() => {
    if (!query.trim()) return [];

    const searchTerm = query.toLowerCase().trim();
    
    return searchableItems
      .map(item => {
        let score = 0;
        
        // Exact title match gets highest score
        if (item.title.toLowerCase() === searchTerm) {
          score += 100;
        }
        // Title starts with search term
        else if (item.title.toLowerCase().startsWith(searchTerm)) {
          score += 80;
        }
        // Title contains search term
        else if (item.title.toLowerCase().includes(searchTerm)) {
          score += 60;
        }
        
        // Description contains search term
        if (item.description.toLowerCase().includes(searchTerm)) {
          score += 30;
        }
        
        // Keywords match
        if (item.keywords) {
          for (const keyword of item.keywords) {
            if (keyword.toLowerCase().includes(searchTerm)) {
              score += 20;
            }
            if (keyword.toLowerCase() === searchTerm) {
              score += 40;
            }
          }
        }
        
        return { ...item, score };
      })
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, 8); // Limit to top 8 results
  }, [query]);

  const handleSearch = useCallback((searchQuery: string) => {
    setQuery(searchQuery);
    setIsOpen(searchQuery.length > 0);
  }, []);

  const handleSelectResult = useCallback((result: SearchResult) => {
    navigate(result.path);
    setQuery('');
    setIsOpen(false);
  }, [navigate]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const clearSearch = useCallback(() => {
    setQuery('');
    setIsOpen(false);
  }, []);

  return {
    query,
    searchResults,
    isOpen,
    handleSearch,
    handleSelectResult,
    handleClose,
    clearSearch
  };
};
