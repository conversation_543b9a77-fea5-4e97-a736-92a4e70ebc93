import { apiClient } from '../lib/api-client';

export interface Notification {
  id: string;
  type: 'appointment_reminder' | 'status_change' | 'new_appointment' | 'cancellation' | 'system' | 'queue_update';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  appointmentId?: number;
  priority: 'low' | 'medium' | 'high';
  data?: any; // Additional notification data
}

export interface NotificationResponse {
  success: boolean;
  data: {
    notifications: Notification[];
    unreadCount: number;
    totalCount: number;
  };
  message: string;
}

export class NotificationService {
  /**
   * Fetch notifications from the API
   */
  static async getNotifications(): Promise<{
    notifications: Notification[];
    unreadCount: number;
    totalCount: number;
  }> {
    const response = await apiClient.get<NotificationResponse>(
      '/api/auth/notifications/mobile/list'
    );
    
    return response.data.data;
  }

  /**
   * Mark a notification as read
   */
  static async markAsRead(notificationId: string): Promise<void> {
    await apiClient.patch(`/api/auth/notifications/${notificationId}/read`);
  }

  /**
   * Mark all notifications as read
   */
  static async markAllAsRead(): Promise<void> {
    await apiClient.patch('/api/auth/notifications/mark-all-read');
  }

  /**
   * Delete a notification
   */
  static async deleteNotification(notificationId: string): Promise<void> {
    await apiClient.delete(`/api/auth/notifications/${notificationId}`);
  }

  /**
   * Get unread notification count
   */
  static async getUnreadCount(): Promise<number> {
    const response = await apiClient.get<{
      success: boolean;
      data: { count: number };
      message: string;
    }>('/api/auth/notifications/unread-count');
    
    return response.data.data.count;
  }
}
