import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useActiveAppointment, useAppointments } from '../../hooks/useAppointments';
import Button from '../ui/button/Button';
import { formatLocalTime } from '../../utils/timezone';

const ActiveSessionWidget: React.FC = () => {
  const navigate = useNavigate();
  const { data: activeAppointment, isLoading, error } = useActiveAppointment();

  // Alternative approach using useAppointments hook
  const { data: allAppointments, isLoading: allLoading } = useAppointments({
    status: ['InProgress']
  });

  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);

  // Find active appointment from all appointments as fallback
  const fallbackActiveAppointment = React.useMemo(() => {
    return allAppointments?.find(apt => apt.status === 'InProgress') || null;
  }, [allAppointments]);

  // Use the active appointment from either hook
  const currentActiveAppointment = activeAppointment || fallbackActiveAppointment;
  const currentIsLoading = isLoading || allLoading;

  // Debug logging
  React.useEffect(() => {
    console.log('ActiveSessionWidget - activeAppointment (dedicated hook):', activeAppointment);
    console.log('ActiveSessionWidget - fallbackActiveAppointment (from useAppointments):', fallbackActiveAppointment);
    console.log('ActiveSessionWidget - currentActiveAppointment:', currentActiveAppointment);
    console.log('ActiveSessionWidget - isLoading:', isLoading);
    console.log('ActiveSessionWidget - allLoading:', allLoading);
    console.log('ActiveSessionWidget - error:', error);
    console.log('ActiveSessionWidget - allAppointments:', allAppointments);
  }, [activeAppointment, fallbackActiveAppointment, currentActiveAppointment, isLoading, allLoading, error, allAppointments]);

  // Calculate time remaining when appointment loads
  useEffect(() => {
    if (currentActiveAppointment) {
      const duration = currentActiveAppointment.serviceDuration || currentActiveAppointment.service?.duration || 30;
      const startTime = currentActiveAppointment.realAppointmentStartTime
        ? new Date(currentActiveAppointment.realAppointmentStartTime)
        : new Date();

      setSessionStartTime(startTime);
      const endTime = new Date(startTime.getTime() + duration * 60000);
      const remaining = Math.max(0, endTime.getTime() - Date.now());
      setTimeRemaining(Math.floor(remaining / 1000));
    }
  }, [currentActiveAppointment]);

  // Timer countdown effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (currentActiveAppointment && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => Math.max(0, prev - 1));
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [currentActiveAppointment, timeRemaining]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleContinueSession = () => {
    if (currentActiveAppointment) {
      navigate(`/service-session/${currentActiveAppointment.id}`);
    }
  };

  if (currentIsLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!currentActiveAppointment) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Active Session
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Start an appointment to begin a service session
          </p>
          <Button
            onClick={() => navigate('/appointments')}
            variant="outline"
            size="sm"
          >
            View Appointments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Active Session
        </h3>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-green-600 dark:text-green-400 font-medium">Live</span>
        </div>
      </div>

      <div className="space-y-4">
        {/* Timer Display */}
        <div className="text-center">
          <div className={`text-3xl font-mono font-bold ${
            timeRemaining <= 300 ? 'text-red-600' : 
            timeRemaining <= 600 ? 'text-yellow-600' : 
            'text-green-600'
          }`}>
            {formatTime(timeRemaining)}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Time Remaining
          </p>
        </div>

        {/* Customer Info */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
              {currentActiveAppointment.customer?.firstName?.charAt(0)}{currentActiveAppointment.customer?.lastName?.charAt(0)}
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white text-sm">
                {currentActiveAppointment.customer?.firstName} {currentActiveAppointment.customer?.lastName}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {currentActiveAppointment.service?.title}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Started:</span>
              <p className="font-medium text-gray-900 dark:text-white">
                {sessionStartTime ? formatLocalTime(sessionStartTime.toISOString()) : 'Now'}
              </p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Duration:</span>
              <p className="font-medium text-gray-900 dark:text-white">
                {currentActiveAppointment.service?.duration || currentActiveAppointment.serviceDuration} min
              </p>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <Button
            onClick={handleContinueSession}
            className="w-full bg-brand-600 hover:bg-brand-700 text-white"
            size="sm"
          >
            Continue Session
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ActiveSessionWidget;
