/**
 * Appointment related types
 */

import { Provider, Service, Location, User } from './index';

export type AppointmentStatus =
  | 'pending'
  | 'confirmed'
  | 'InProgress'
  | 'completed'
  | 'canceled'
  | 'noshow';

export type RealTimeStatus = 'ontime' | 'delayed' | 'early';

export interface Appointment {
  id: number;
  sProviderId?: number;
  customerUserId?: string;
  serviceId?: number;
  placeId?: number;
  queueId?: number;
  status: AppointmentStatus;
  expectedAppointmentStartTime: string;
  expectedAppointmentEndTime: string;
  realAppointmentStartTime?: string;
  realAppointmentEndTime?: string;
  serviceDuration?: number; // in minutes
  notes?: string;
  realTimeStatus?: RealTimeStatus;
  slots?: number;
  isOverflowed?: boolean;
  overflowReason?: string;
  createdAt?: string;
  updatedAt?: string;

  // Relations (API response structure)
  provider?: Provider;
  customer?: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
  };
  service?: {
    id: number;
    title: string;
    duration: number;
    price: number;
    pointsRequirements?: number;
    isPublic?: boolean;
    deliveryType?: string;
    servedRegions?: any[];
    color?: string;
    acceptOnline?: boolean;
    acceptNew?: boolean;
    notificationOn?: boolean;
  };
  place?: {
    id: number;
    name: string;
    isMobileHidden?: boolean;
    parking?: boolean;
    elevator?: boolean;
    handicapAccess?: boolean;
    queues?: any[];
  };
  queue?: {
    id: number;
    title: string;
    isActive: boolean;
    sProvidingPlaceId: number;
    services: any[];
  };
  history?: AppointmentHistory[];
  rescheduleRequests?: RescheduleRequest[];
}

export interface AppointmentHistory {
  id: number;
  appointmentId: number;
  changedByUserId: string;
  previousStartTime: string;
  previousEndTime: string;
  newStartTime: string;
  newEndTime: string;
  previousStatus: AppointmentStatus;
  newStatus: AppointmentStatus;
  changeReason: string;
  createdAt: string;
  
  // Relations
  appointment?: Appointment;
  changedBy?: User;
}

export interface AppointmentCreateRequest {
  customerId: string; // API expects 'customerId' not 'customerUserId'
  serviceId?: number;
  placeId?: number;
  queueId: number; // Required according to documentation
  expectedStartTime: string; // API expects 'expectedStartTime' not 'expectedAppointmentStartTime'
  expectedEndTime: string; // API expects 'expectedEndTime' not 'expectedAppointmentEndTime'
  serviceDuration: number;
  notes?: string;
  slots?: number;
}

export interface AppointmentUpdateRequest {
  id: number;
  status?: AppointmentStatus;
  expectedAppointmentStartTime?: string;
  expectedAppointmentEndTime?: string;
  realAppointmentStartTime?: string;
  realAppointmentEndTime?: string;
  notes?: string;
  realTimeStatus?: RealTimeStatus;
  changeReason?: string;
}

export interface AppointmentStatusUpdate {
  status: AppointmentStatus;
  changeReason?: string;
  realAppointmentStartTime?: string;
  realAppointmentEndTime?: string;
}

export interface AppointmentExtendRequest {
  appointmentId: number;
  additionalMinutes: number;
  reason?: string;
}

export interface RescheduleRequest {
  id: number;
  appointmentId: number;
  requestedByUserId: string;
  currentStartTime: string;
  currentEndTime: string;
  requestedStartTime: string;
  requestedEndTime: string;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  providerResponse?: string;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  appointment?: Appointment;
  requestedBy?: User;
}

export interface RescheduleResponse {
  rescheduleId: number;
  status: 'approved' | 'rejected';
  response?: string;
  alternativeStartTime?: string;
  alternativeEndTime?: string;
}

export interface AppointmentFilters {
  status?: AppointmentStatus | AppointmentStatus[];
  serviceId?: number;
  locationId?: number;
  queueId?: number;
  customerId?: string;
  startDate?: string;
  endDate?: string;
  realTimeStatus?: RealTimeStatus;
  isOverflowed?: boolean;
}

export interface AppointmentStats {
  totalAppointments: number;
  todayAppointments: number;
  upcomingAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  noShowAppointments: number;
  averageDuration: number;
  occupancyRate: number;
  revenueToday: number;
  revenueThisMonth: number;
}

export interface AppointmentTimeSlot {
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  conflictReason?: string;
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  extendedProps: {
    appointmentId: number;
    status: AppointmentStatus;
    customerName: string;
    serviceName?: string;
    locationName?: string;
    notes?: string;
  };
}

// Forward declarations
export interface Queue {
  id: number;
  // Other queue fields...
}
